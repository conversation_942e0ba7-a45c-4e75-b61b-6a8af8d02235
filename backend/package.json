{"name": "finscope-backend", "version": "1.0.0", "description": "FinScope Backend API", "main": "dist/src/index.js", "scripts": {"clean": "rm -rf dist && find src -name '*.js' -delete && find src -name '*.d.ts' -delete && find src -name '*.map' -delete", "prebuild": "npm run clean", "start": "node dist/src/index.js", "dev": "concurrently \"npm:dev:api\" \"npm:dev:worker\"", "dev:api": "nodemon -r dotenv/config src/index.ts", "dev:worker": "nodemon -r dotenv/config services/document-processor/src/queue-worker.ts", "build": "tsc", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["finscope", "finance", "ai", "api"], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"@google/genai": "^1.6.0", "@supabase/supabase-js": "^2.52.0", "axios": "^1.10.0", "bull": "^4.16.5", "cors": "^2.8.5", "csv-parse": "^6.0.0", "dotenv": "^16.6.1", "express": "^4.21.2", "form-data": "^4.0.4", "google-auth-library": "^9.15.1", "helmet": "^8.1.0", "morgan": "^1.10.1", "multer": "^1.4.5-lts.1", "pdf-parse": "^1.1.1", "pdf2pic": "^3.2.0", "sharp": "^0.34.3", "tesseract.js": "^6.0.1", "uuid": "^11.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/bull": "^3.15.9", "@types/cors": "^2.8.19", "@types/csv-parse": "^1.1.12", "@types/express": "^4.17.23", "@types/form-data": "^2.2.1", "@types/morgan": "^1.9.10", "@types/multer": "^1.4.13", "@types/node": "^22.16.5", "@types/pdf-parse": "^1.1.5", "@types/uuid": "^10.0.0", "@types/xlsx": "^0.0.35", "concurrently": "^9.2.0", "nodemon": "^3.1.0", "typescript": "~5.7.2"}}