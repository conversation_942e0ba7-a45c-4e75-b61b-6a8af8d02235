export * from '../services/supabase';

export type TransactionType = 'credit' | 'debit';

export interface TransactionBase {
  id?: string;
  user_id: string;
  amount: number;
  date: string;
  description: string;
  category?: string;
  vendor?: string;
  type: TransactionType;
  created_at?: string;
  updated_at?: string;
  fingerprint?: string;
}

export interface TransactionStats {
  totalIncome: number;
  totalExpenses: number;
  netCashFlow: number;
  transactionCount: number;
  categoryBreakdown: Record<string, number>;
}
