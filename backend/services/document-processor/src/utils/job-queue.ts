import Queue from 'bull';
import Redis from 'ioredis';
import { DocumentProcessor } from '../document-processor';
import { SupabaseDocumentService } from './supabase-service';
import { ProcessingOptions } from '../types';

// Circuit breaker for Redis connection
class CircuitBreaker {
  private failures = 0;
  private lastFailureTime = 0;
  private state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED';

  constructor(
    private readonly failureThreshold = 5,
    private readonly recoveryTimeout = 30000 // 30 seconds
  ) {}

  canExecute(): boolean {
    if (this.state === 'CLOSED') return true;

    if (this.state === 'OPEN') {
      if (Date.now() - this.lastFailureTime > this.recoveryTimeout) {
        this.state = 'HALF_OPEN';
        return true;
      }
      return false;
    }

    return true; // HALF_OPEN
  }

  onSuccess(): void {
    this.failures = 0;
    this.state = 'CLOSED';
  }

  onFailure(): void {
    this.failures++;
    this.lastFailureTime = Date.now();

    if (this.failures >= this.failureThreshold) {
      this.state = 'OPEN';
    }
  }

  getState(): string {
    return this.state;
  }
}

// Redis connection configuration with resilience
const redisUrl = process.env.REDIS_URL;

if (!redisUrl) {
  throw new Error('Redis URL is not set. Please set REDIS_URL in your .env file.');
}

// Parse Redis URL for ioredis configuration
const redisConfig = {
  maxRetriesPerRequest: 3,
  retryDelayOnFailover: 100,
  enableReadyCheck: false,
  maxLoadingTimeout: 5000,
  connectTimeout: 10000,
  commandTimeout: 5000,
  lazyConnect: true,
  keepAlive: 30000,
  family: 4,
  // Exponential backoff configuration
  retryDelayOnClusterDown: 300,
  // Enable offline queue for better error handling during shutdown
  enableOfflineQueue: true,
  // Limit offline queue size to prevent memory issues
  maxOfflineQueue: 100,
};

// Circuit breaker instance
const circuitBreaker = new CircuitBreaker(5, 30000);

// Create Redis connection with proper error handling
const createRedisConnection = () => {
  const redis = new Redis(redisUrl!, redisConfig);

  redis.on('error', (error) => {
    console.error('❌ Redis connection error:', error.message);
    circuitBreaker.onFailure();
  });

  redis.on('connect', () => {
    console.log('✅ Redis connected successfully');
    circuitBreaker.onSuccess();
  });

  redis.on('ready', () => {
    console.log('🚀 Redis ready for operations');
  });

  redis.on('close', () => {
    console.log('🔌 Redis connection closed');
  });

  redis.on('reconnecting', (delay: number) => {
    console.log(`🔄 Redis reconnecting in ${delay}ms...`);
  });

  return redis;
};

// Create document processing queue with optimized Redis configuration
const documentQueue = new Queue('document-processing', redisUrl!, {
  redis: redisConfig,
  defaultJobOptions: {
    attempts: 3,
    backoff: {
      type: 'exponential',
      delay: 5000, // Increased delay to reduce connection pressure
    },
    removeOnComplete: 50, // Reduced to save memory
    removeOnFail: 25,     // Reduced to save memory
  },
  settings: {
    stalledInterval: 30000,    // Check for stalled jobs every 30s instead of default 30s
    maxStalledCount: 1,        // Reduce stalled job retries
    retryProcessDelay: 5000,   // Delay before retrying failed jobs
  }
});

// Enhanced job processor with circuit breaker protection
documentQueue.process(async (job) => {
  const {
    fileBuffer,
    mimeType,
    userId,
    fileName,
    options,
    jobId
  } = job.data;

  console.log(`🔄 Processing job ${jobId} for user ${userId}: ${fileName} (Circuit breaker: ${circuitBreaker.getState()})`);

  // Check circuit breaker before processing
  if (!circuitBreaker.canExecute()) {
    const error = new Error('Circuit breaker is OPEN - Redis connection issues detected');
    console.error(`🚫 Job ${jobId} rejected due to circuit breaker`);

    await SupabaseDocumentService.updateJobStatus(jobId, {
      status: 'failed',
      failedAt: new Date(),
      error: error.message
    }).catch(err => console.error('Failed to update job status:', err));

    throw error;
  }

  try {
    // Update job status to processing with retry logic
    await retryWithBackoff(async () => {
      await SupabaseDocumentService.updateJobStatus(jobId, {
        status: 'processing',
        startedAt: new Date(),
        progress: 0
      });
    }, 3, 1000);

    // Process the document
    const result = await DocumentProcessor.processDocument(
      fileBuffer,
      mimeType,
      userId,
      fileName,
      options
    );

    if (result.success) {
      // Update job status to completed with retry logic
      await retryWithBackoff(async () => {
        await SupabaseDocumentService.updateJobStatus(jobId, {
          status: 'completed',
          completedAt: new Date(),
          progress: 100,
          result: {
            transactionCount: result.extractedData?.transactions?.length || 0,
            processingTime: result.processingTime || 0,
            confidence: result.confidence?.overall || 0
          }
        });
      }, 3, 1000);

      console.log(`✅ Job ${jobId} completed successfully`);
      circuitBreaker.onSuccess(); // Mark success for circuit breaker
      return result;
    } else {
      throw new Error(result.error || 'Document processing failed');
    }

  } catch (error) {
    console.error(`❌ Job ${jobId} failed:`, error);
    circuitBreaker.onFailure(); // Mark failure for circuit breaker

    // Update job status to failed with retry logic
    try {
      await retryWithBackoff(async () => {
        await SupabaseDocumentService.updateJobStatus(jobId, {
          status: 'failed',
          failedAt: new Date(),
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }, 3, 1000);
    } catch (updateError) {
      console.error(`Failed to update job status for ${jobId}:`, updateError);
    }

    throw error;
  }
});

// Utility function for retry with exponential backoff
async function retryWithBackoff<T>(
  operation: () => Promise<T>,
  maxRetries: number,
  baseDelay: number
): Promise<T> {
  let lastError: Error;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error as Error;

      if (attempt === maxRetries) {
        throw lastError;
      }

      const delay = baseDelay * Math.pow(2, attempt);
      console.log(`Retry attempt ${attempt + 1}/${maxRetries + 1} in ${delay}ms...`);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw lastError!;
}

// Enhanced job progress tracking with error handling
documentQueue.on('progress', async (job, progress) => {
  const { jobId } = job.data;
  try {
    await retryWithBackoff(async () => {
      await SupabaseDocumentService.updateJobStatus(jobId, {
        progress: progress
      });
    }, 2, 500);
  } catch (error) {
    console.error(`Failed to update progress for job ${jobId}:`, error);
  }
});

// Enhanced job completion tracking
documentQueue.on('completed', async (job, _result) => {
  const { jobId, userId } = job.data;
  console.log(`🎉 Job ${jobId} completed for user ${userId}`);
  circuitBreaker.onSuccess(); // Mark success for circuit breaker
});

// Enhanced job failure tracking
documentQueue.on('failed', async (job, err) => {
  const { jobId, userId } = job.data;
  console.error(`💥 Job ${jobId} failed for user ${userId}:`, err.message);
  circuitBreaker.onFailure(); // Mark failure for circuit breaker
});

// Queue error handling
documentQueue.on('error', (error) => {
  console.error('🚨 Queue error:', error);
  circuitBreaker.onFailure();
});

// Queue stalled job handling
documentQueue.on('stalled', (job) => {
  console.warn(`⚠️ Job ${job.data.jobId} stalled and will be retried`);
});

// Connection monitoring
let connectionCheckInterval: NodeJS.Timeout;

const startConnectionMonitoring = () => {
  connectionCheckInterval = setInterval(async () => {
    try {
      const stats = await documentQueue.getJobCounts();
      console.log(`📊 Queue stats - Waiting: ${stats.waiting}, Active: ${stats.active}, Completed: ${stats.completed}, Failed: ${stats.failed} | Circuit breaker: ${circuitBreaker.getState()}`);
    } catch (error) {
      console.error('❌ Failed to get queue stats:', error);
      circuitBreaker.onFailure();
    }
  }, 60000); // Check every minute
};

// Start monitoring
startConnectionMonitoring();

export class JobQueueService {
  /**
   * Add a document processing job to the queue with circuit breaker protection
   */
  static async addDocumentJob(
    fileBuffer: Buffer,
    mimeType: string,
    userId: string,
    fileName: string,
    options: ProcessingOptions = {}
  ): Promise<string> {
    // Check circuit breaker before adding job
    if (!circuitBreaker.canExecute()) {
      throw new Error('Cannot add job: Redis connection issues detected (Circuit breaker is OPEN)');
    }

    const jobId = `job_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    try {
      // Create initial job status with retry logic
      await retryWithBackoff(async () => {
        await SupabaseDocumentService.createJobStatus({
          jobId,
          userId,
          fileName,
          status: 'queued',
          createdAt: new Date(),
          fileSize: fileBuffer.length,
          mimeType,
          options
        });
      }, 3, 1000);

      // Add job to queue with timeout
      const job = await Promise.race([
        documentQueue.add({
          fileBuffer,
          mimeType,
          userId,
          fileName,
          options,
          jobId
        }),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Job addition timeout')), 10000)
        )
      ]) as any;

      console.log(`📝 Job ${jobId} added to queue (job.id: ${job.id})`);
      circuitBreaker.onSuccess(); // Mark success
      return jobId;
    } catch (error) {
      console.error(`Failed to add job ${jobId}:`, error);
      circuitBreaker.onFailure(); // Mark failure

      // Try to update job status to failed
      try {
        await SupabaseDocumentService.updateJobStatus(jobId, {
          status: 'failed',
          failedAt: new Date(),
          error: error instanceof Error ? error.message : 'Failed to queue job'
        });
      } catch (updateError) {
        console.error(`Failed to update job status for ${jobId}:`, updateError);
      }

      throw error;
    }
  }

  /**
   * Get job status with error handling
   */
  static async getJobStatus(jobId: string): Promise<any> {
    try {
      return await retryWithBackoff(async () => {
        return await SupabaseDocumentService.getJobStatus(jobId);
      }, 2, 500);
    } catch (error) {
      console.error(`Failed to get job status for ${jobId}:`, error);
      throw error;
    }
  }

  /**
   * Get user's job history with error handling
   */
  static async getUserJobs(userId: string, limit: number = 20): Promise<any[]> {
    try {
      return await retryWithBackoff(async () => {
        return await SupabaseDocumentService.getUserJobs(userId, limit);
      }, 2, 500);
    } catch (error) {
      console.error(`Failed to get user jobs for ${userId}:`, error);
      throw error;
    }
  }

  /**
   * Cancel a job with enhanced error handling
   */
  static async cancelJob(jobId: string): Promise<boolean> {
    if (!circuitBreaker.canExecute()) {
      console.warn(`Cannot cancel job ${jobId}: Circuit breaker is OPEN`);
      return false;
    }

    try {
      // Find the job in the queue with timeout
      const jobs = await Promise.race([
        documentQueue.getJobs(['waiting', 'active']),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Get jobs timeout')), 5000)
        )
      ]) as any[];

      const job = jobs.find(j => j.data.jobId === jobId);

      if (job) {
        await Promise.race([
          job.remove(),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('Job removal timeout')), 5000)
          )
        ]);

        await retryWithBackoff(async () => {
          await SupabaseDocumentService.updateJobStatus(jobId, {
            status: 'cancelled',
            cancelledAt: new Date()
          });
        }, 3, 1000);

        circuitBreaker.onSuccess();
        return true;
      }

      return false;
    } catch (error) {
      console.error('Error cancelling job:', error);
      circuitBreaker.onFailure();
      return false;
    }
  }

  /**
   * Get queue statistics with circuit breaker protection
   */
  static async getQueueStats(): Promise<any> {
    if (!circuitBreaker.canExecute()) {
      return {
        waiting: 0,
        active: 0,
        completed: 0,
        failed: 0,
        total: 0,
        circuitBreakerState: circuitBreaker.getState(),
        error: 'Circuit breaker is OPEN'
      };
    }

    try {
      const stats = await Promise.race([
        documentQueue.getJobCounts(),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Queue stats timeout')), 5000)
        )
      ]) as any;

      circuitBreaker.onSuccess();
      return {
        ...stats,
        circuitBreakerState: circuitBreaker.getState()
      };
    } catch (error) {
      console.error('Error getting queue stats:', error);
      circuitBreaker.onFailure();
      return {
        waiting: 0,
        active: 0,
        completed: 0,
        failed: 0,
        total: 0,
        circuitBreakerState: circuitBreaker.getState(),
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Clean up old jobs with error handling
   */
  static async cleanupOldJobs(daysOld: number = 7): Promise<void> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysOld);

      await retryWithBackoff(async () => {
        await SupabaseDocumentService.cleanupOldJobs(cutoffDate);
      }, 3, 2000);

      console.log(`🧹 Cleaned up jobs older than ${daysOld} days`);
    } catch (error) {
      console.error('Failed to cleanup old jobs:', error);
      throw error;
    }
  }

  /**
   * Get circuit breaker status
   */
  static getCircuitBreakerStatus(): { state: string; failures: number } {
    return {
      state: circuitBreaker.getState(),
      failures: (circuitBreaker as any).failures || 0
    };
  }

  /**
   * Reset circuit breaker (for manual recovery)
   */
  static resetCircuitBreaker(): void {
    circuitBreaker.onSuccess();
    console.log('🔄 Circuit breaker manually reset');
  }
}

// Enhanced graceful shutdown with proper cleanup
let isShuttingDown = false;

const gracefulShutdown = async (signal: string) => {
  if (isShuttingDown) {
    console.log(`${signal} received again, forcing exit...`);
    process.exit(1);
  }

  isShuttingDown = true;
  console.log(`${signal} received, starting graceful shutdown...`);

  try {
    // Clear monitoring interval
    if (connectionCheckInterval) {
      clearInterval(connectionCheckInterval);
    }

    // Wait for active jobs to complete (with timeout)
    const activeJobs = await documentQueue.getActive();
    if (activeJobs.length > 0) {
      console.log(`⏳ Waiting for ${activeJobs.length} active jobs to complete...`);

      // Wait up to 30 seconds for jobs to complete
      const timeout = setTimeout(() => {
        console.log('⚠️ Shutdown timeout reached, forcing close...');
      }, 30000);

      // Check every second if jobs are done
      while ((await documentQueue.getActive()).length > 0) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      clearTimeout(timeout);
    }

    // Close the queue
    await documentQueue.close();
    console.log('✅ Job queue closed successfully');

    process.exit(0);
  } catch (error) {
    console.error('❌ Error during graceful shutdown:', error);
    process.exit(1);
  }
};

process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('🚨 Uncaught Exception:', error);
  circuitBreaker.onFailure();
  gracefulShutdown('UNCAUGHT_EXCEPTION');
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('🚨 Unhandled Rejection at:', promise, 'reason:', reason);
  circuitBreaker.onFailure();
});

export default JobQueueService;