import dotenv from 'dotenv';
dotenv.config({ path: '.env' });

import JobQueueService from './utils/job-queue';

console.log('Starting document processing queue worker...');

// The JobQueueService is already initialized in its own file, so we just need to import it here.
// This will start the queue and begin processing jobs.

const worker = JobQueueService;

console.log('Document processing queue worker started successfully.');

// Keep the process alive
setInterval(() => {
    // No-op, just to keep the process running
}, 1000 * 60 * 60); // Keep alive for 1 hour
