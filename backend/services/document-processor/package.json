{"name": "document-processor", "version": "1.0.0", "description": "FinScope Document Processor Microservice", "main": "dist/index.js", "scripts": {"dev": "nodemon -r dotenv/config src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "echo \"Error: no test specified\" && exit 1", "clean": "rm -rf dist", "prebuild": "npm run clean"}, "keywords": ["finscope", "microservice", "document-processing"], "author": "FinScope Team", "license": "ISC", "type": "commonjs", "dependencies": {"@types/bull": "^3.15.9", "bull": "^4.16.5", "ioredis": "^5.6.1"}, "devDependencies": {"nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}